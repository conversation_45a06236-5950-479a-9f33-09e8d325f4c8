from PIL.Image import Image

def crop_corners(image: Image, corner_width: int, corner_height: int):
    width, height = image.size

    return [
        image.crop((0, 0, corner_width, corner_height)), # top-left
        image.crop((max(0, width - corner_width), 0, width, corner_height)),  # top-right
        image.crop((0, max(0, height - corner_height), corner_width, height)),  # bottom-left
        image.crop((max(0, width - corner_width), max(0, height - corner_height), width, height))  # bottom-right
    ]