import tensorflow as tf

from werkzeug.exceptions import BadRequest, InternalServerError, UnprocessableEntity, Unauthorized
from werkzeug.wrappers import Response
from flask import Flask, request, current_app
from functools import wraps
from PIL import Image
from keras.applications.resnet_v2 import preprocess_input
from keras.models import load_model
from helpers.image import crop_corners

app = Flask(__name__)
app.config.from_mapping(API_KEY='secret')

model = load_model('checkpoints/model.keras', compile=False)
_, input_width, input_height, _ = model.input.shape

class InvalidImageFormatException(Exception):
    pass

class LogoNotFoundException(Exception):
    def __init__(self, images: list[str]):
        super().__init__('Logo not found in images')

        self.images = images

class UnprocessableEntityWithDetails(UnprocessableEntity):
    def __init__(self, description: str | None = None, response: Response | None = None, details: list[str] | None = None):
        super().__init__(description, response)

        self.details = details

@app.errorhandler(BadRequest)
def handle_bad_request(error: BadRequest):
    return {
        'code': 'BAD_REQUEST',
        'message': error.description
    }, error.code

@app.errorhandler(InternalServerError)
def handle_bad_request(error: InternalServerError):
    return {
        'code': 'INTERNAL_SERVER_ERROR',
        'message': error.description
    }, error.code

@app.errorhandler(UnprocessableEntityWithDetails)
def handle_bad_request(error: UnprocessableEntityWithDetails):
    return {
        'code': 'UNPROCESSABLE_ENTITY',
        'message': error.description,
        'details': error.details
    }, error.code

@app.errorhandler(Unauthorized)
def handle_bad_request(error: Unauthorized):
    return {
        'code': 'UNAUTHORIZED',
        'message': error.description
    }, error.code

def api_key_required():
    def decorator(function: callable):
        @wraps(function)
        def wrapper(*args, **kwargs):
            api_key_header = request.headers.get('X-API-Key')

            if api_key_header != current_app.config['API_KEY']:
                raise Unauthorized('Invalid API key')

            return function(*args, **kwargs)

        return wrapper

    return decorator

@app.route('/images/validate', methods=['POST'])
@api_key_required()
def validate_images():
    image_files = request.files.getlist('images')

    if not image_files or len(image_files) == 0:
        raise BadRequest('No images provided')

    try:
        corners = []

        for image_file in image_files:
            with Image.open(image_file.stream) as image:
                if image.format not in ['JPEG', 'PNG', 'WEBP']:
                    raise InvalidImageFormatException(f'Invalid image format "{image.format}" for "{image_file.filename}"')

                corners = corners + [preprocess_input(tf.convert_to_tensor(corner, dtype=tf.float32)) for corner in crop_corners(image, input_width, input_height)]

        corners = tf.convert_to_tensor(corners)

        result = model(corners, training=False)
        result = tf.reduce_any(tf.reshape(result, (-1, 4)) > 0.5, axis=1)

        invalid_images = [image_files[index].filename for index, is_valid in enumerate(result) if not is_valid]

        if len(invalid_images) > 0:
            raise LogoNotFoundException(invalid_images)

        return '', 204
    except InvalidImageFormatException as exception:
        raise BadRequest(str(exception))
    except LogoNotFoundException as exception:
        raise UnprocessableEntityWithDetails(f'Logo not found in images', details=exception.images)
    except Exception as exception:
        raise InternalServerError(f'Error processing images: {exception}')