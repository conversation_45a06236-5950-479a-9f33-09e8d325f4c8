import sys
import argparse

from keras.callbacks import EarlyStopping
from keras.utils import image_dataset_from_directory
from keras.applications.resnet_v2 import preprocess_input, ResNet50V2
from keras.layers import GlobalAveragePooling2D, Dense
from keras.models import Model, load_model
from keras.optimizers import Adam
from keras.losses import BinaryCrossentropy
from keras.metrics import BinaryAccuracy

from typing import List

def parse_args(argv: List[str]) -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description=(
            'Train a model to detect the ancor logo in images.'
        )
    )

    parser.add_argument(
        '--dataset_path',
        type=str,
        required=True,
        help='Path to the directory containing the dataset',
    )

    parser.add_argument(
        '--model_path',
        type=str,
        default=None,
        help='Path of the model to load. None to train from model from scratch.',
    )
    
    parser.add_argument(
        '--batch_size',
        type=int,
        default=32,
        help='Batch size for training (default: 32)',
    )

    parser.add_argument(
        '--epochs',
        type=int,
        default=50,
        help='Number of epochs to train (default: 50)',
    )

    parser.add_argument(
        '--model_output_path',
        type=str,
        default='model.keras',
        help='Path to save the trained model (default: model.keras)',
    )

    return parser.parse_args(argv)

def build_model():
    base_model = ResNet50V2(weights='imagenet', include_top=False, input_shape=(224, 224, 3))
    base_model.trainable = False

    x = base_model.output
    x = GlobalAveragePooling2D()(x)
    x = Dense(128, activation='relu')(x)
    outputs = Dense(1, activation='sigmoid')(x)

    model = Model(inputs=base_model.input, outputs=outputs)
    model.compile(optimizer=Adam(), loss=BinaryCrossentropy(), metrics=[BinaryAccuracy()])

    return model

def save_model(model: Model, path: str):
    model.save(path)

def get_datasets(dataset_path: str, batch_size: int = 32, image_size: tuple = (224, 224)) -> tuple:
    training_set, validation_set = image_dataset_from_directory(dataset_path, label_mode='binary', image_size=image_size, subset='both', validation_split=0.2, batch_size=batch_size, seed=42)

    training_set = training_set.map(lambda x, y: (preprocess_input(x), y))
    validation_set = validation_set.map(lambda x, y: (preprocess_input(x), y))

    return training_set, validation_set

if __name__ == '__main__':
    args = parse_args(sys.argv[1:])

    if args.model_path:
        model = load_model(args.model_path)
    else:
        model = build_model()

    _batch_size, input_width, input_height, _channels = model.input.shape
    training_set, validation_set = get_datasets(args.dataset_path, args.batch_size, (input_width, input_height))

    model.fit(training_set, validation_data=validation_set, epochs=args.epochs, callbacks=[
        EarlyStopping(patience=3, restore_best_weights=True)])

    save_model(model, args.model_output_path)
