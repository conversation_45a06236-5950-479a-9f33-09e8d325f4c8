from __future__ import annotations

import argparse
import sys
import numpy as np
import cv2
from pathlib import Path
from typing import Iterable, List, Optional
from PIL import Image, UnidentifiedImageError
from helpers.image import crop_corners

SUPPORTED_EXTS = {".jpg", ".jpeg", ".png", ".webp"}
CORNER_LABELS = ("top_left", "top_right", "bottom_left", "bottom_right")

def parse_args(argv: List[str]) -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description=(
            "Crop 4 corners (top-left, top-right, bottom-left, bottom-right) from all images "
            "in a directory."
        )
    )
    parser.add_argument(
        "--images_path",
        type=str,
        required=True,
        help="Path to the directory containing images (non-recursive)",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default="dataset",
        help='Output directory for cropped images (default: "dataset")',
    )
    parser.add_argument(
        "--extensions",
        type=str,
        default=",".join(sorted(SUPPORTED_EXTS)),
        help=(
            "Comma-separated list of file extensions to include. "
            f"Default: {','.join(sorted(SUPPORTED_EXTS))}"
        ),
    )
    return parser.parse_args(argv)

def is_image_file(path: Path, allowed_exts: Iterable[str]) -> bool:
    return path.is_file() and path.suffix.lower() in allowed_exts

def list_image_files(images_dir: Path, allowed_exts: Iterable[str]) -> List[Path]:
    return sorted([p for p in images_dir.iterdir() if is_image_file(p, allowed_exts)])

def ensure_unique_path(base_path: Path) -> Path:
    """
    If base_path exists, append _001, _002, ... before the suffix to avoid overwriting.
    """
    if not base_path.exists():
        return base_path
    stem = base_path.stem
    suffix = base_path.suffix
    parent = base_path.parent
    idx = 1
    while True:
        candidate = parent / f"{stem}_{idx:03d}{suffix}"
        if not candidate.exists():
            return candidate
        idx += 1


def display_image_cv2(image: Image.Image, title: str) -> Optional[str]:
    """Display image using OpenCV and return user classification."""
    # Convert PIL image to OpenCV format
    img_array = np.array(image)
    img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

    # Resize for better display if too small
    h, w = img_bgr.shape[:2]
    if w < 200 or h < 200:
        scale = max(200 / w, 200 / h)
        new_w, new_h = int(w * scale), int(h * scale)
        img_bgr = cv2.resize(img_bgr, (new_w, new_h), interpolation=cv2.INTER_NEAREST)

    cv2.imshow(title, img_bgr)

    print(f"\nClassifying: {title}")
    print("Press: 'v' for Valid, 'i' for Invalid, 's' to Skip, 'q' to Quit, 'n' for Next image")

    while True:
        key = cv2.waitKey(0) & 0xFF
        if key == ord('v'):
            cv2.destroyAllWindows()
            return "valid"
        elif key == ord('i'):
            cv2.destroyAllWindows()
            return "invalid"
        elif key == ord('s'):
            cv2.destroyAllWindows()
            return "skip"
        elif key == ord('q'):
            cv2.destroyAllWindows()
            return "quit"
        elif key == ord('n'):
            cv2.destroyAllWindows()
            return "next"
        elif key == 27:  # ESC key
            cv2.destroyAllWindows()
            return "quit"

def classify_image(image: Image.Image, title: str) -> Optional[str]:
    """Display image and get user classification."""
    return display_image_cv2(image, title)


def process_image(img_path: Path, out_dir: Path) -> List[Path]:
    saved_paths: List[Path] = []
    try:
        with Image.open(img_path) as im:
            # Convert to RGB to ensure consistent saving across formats
            im_converted = im.convert("RGB")
            crop_width = 224
            crop_height = 224

            ext = img_path.suffix  # keep original extension for naming
            base_stem = img_path.stem

            # Create subdirectories for valid/invalid
            valid_dir = out_dir / "valid"
            invalid_dir = out_dir / "invalid"
            valid_dir.mkdir(parents=True, exist_ok=True)
            invalid_dir.mkdir(parents=True, exist_ok=True)

            cropped_images = zip(CORNER_LABELS, crop_corners(im_converted, crop_width, crop_height))

            # Interactive classification
            for cropped, label in cropped_images:
                title = f"{img_path.name} - {label.replace('_', ' ').title()}"
                classification = classify_image(cropped, title)

                if classification == "quit":
                    print("\nQuitting...")
                    return saved_paths
                elif classification == "next":
                    print(f"Skipping remaining corners of {img_path.name}")
                    break
                elif classification == "skip":
                    print(f"Skipping {label}")
                    continue
                elif classification in ["valid", "invalid"]:
                    target_dir = valid_dir if classification == "valid" else invalid_dir
                    out_name = f"{base_stem}_{label}{ext}"
                    out_path = ensure_unique_path(target_dir / out_name)
                    try:
                        cropped.save(out_path)
                        saved_paths.append(out_path)
                        print(f"Saved as {classification}: {out_path}")
                    except Exception as save_err:
                        print(
                            f"    Error saving {out_path.name}: {save_err}",
                            file=sys.stderr,
                        )
    except UnidentifiedImageError:
        print(f"  Skipping (unidentified/corrupt): {img_path.name}", file=sys.stderr)
    except OSError as e:
        # Common for truncated/corrupt files
        print(f"  Skipping (OS error): {img_path.name} -> {e}", file=sys.stderr)
    except Exception as e:
        print(f"  Error processing {img_path.name}: {e}", file=sys.stderr)

    return saved_paths


def main(argv: List[str]) -> int:
    args = parse_args(argv)

    images_dir = Path(args.images_path).expanduser().resolve()
    if not images_dir.exists() or not images_dir.is_dir():
        print(f"Images path is not a directory: {images_dir}", file=sys.stderr)
        return 2

    try:
        allowed_exts = {e.strip().lower() if e.strip().startswith(".") else f".{e.strip().lower()}" for e in args.extensions.split(",") if e.strip()}
    except Exception:
        allowed_exts = SUPPORTED_EXTS

    output_dir = Path(args.output_dir).expanduser().resolve()
    output_dir.mkdir(parents=True, exist_ok=True)

    print("\nControls:")
    print("  'v' = Valid (save to dataset/valid/)")
    print("  'i' = Invalid (save to dataset/invalid/)")
    print("  's' = Skip this corner")
    print("  'n' = Skip to next image")
    print("  'q' = Quit")
    print("  ESC = Quit")

    image_files = list_image_files(images_dir, allowed_exts)
    if not image_files:
        print(f"No images found in {images_dir} with extensions: {', '.join(sorted(allowed_exts))}")
        return 0

    total = len(image_files)
    print(f"Found {total} image(s) in {images_dir}.")
    print(f"Output will be saved to {output_dir}.")

    overall_saved = 0
    try:
        for idx, img_path in enumerate(image_files, start=1):
            print(f"\n[{idx}/{total}] Processing: {img_path.name}")
            saved = process_image(img_path, output_dir)
            overall_saved += len(saved)
    except KeyboardInterrupt:
        print("\nInterrupted by user.")
        return 1

    print(f"\nDone. Saved {overall_saved} cropped image(s) to {output_dir}.")
    return 0


if __name__ == "__main__":
    sys.exit(main(sys.argv[1:]))

