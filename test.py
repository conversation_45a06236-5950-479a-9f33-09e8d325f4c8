from keras.models import load_model
from keras.applications.resnet_v2 import preprocess_input
from PIL import Image
from helpers.image import crop_corners

import tensorflow as tf

if __name__ == '__main__':
    model = load_model('checkpoints/model.keras', compile=False)
    _batch_size, input_width, input_height, _channels = model.input.shape

    with Image.open('PXL_20250814_091819633_cut.jpg') as image:
        image = image.convert('RGB')
        corners = crop_corners(image, input_width, input_height)

        corners = tf.convert_to_tensor([preprocess_input(tf.convert_to_tensor(corner, dtype=tf.float32)) for corner in corners])
        
        result = model(corners, training=False)
        result = tf.where(result > 0.5, 1, 0)