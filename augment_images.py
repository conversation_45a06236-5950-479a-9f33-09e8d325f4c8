import glob
import cv2
import albumentations as A

from os.path import join

if __name__ == '__main__':
    images_path = 'dataset/valid'
    images = glob.glob(f'{images_path}/*.jpg')
    transforms = {
        'blur': <PERSON><PERSON>(p=1),
        'clahe': <PERSON><PERSON>(p=1),
        'sharpen': <PERSON><PERSON>(p=1),
        'random_brightness_contrast': A.<PERSON>rightnessContrast(p=1, brightness_limit=(0.2, 0.4), contrast_limit=(-0.4, -0.2)),
        'iso_noise': A.<PERSON>(p=1),
    }

    for image_path in images:
        image_name = image_path.split('/')[-1]
        parts = image_name.split('.')

        image_name = '.'.join(parts[:-1])
        image_extension = parts[-1]
        image = cv2.imread(image_path, cv2.IMREAD_COLOR_RGB)

        for transform in transforms:
            transform_fn = transforms[transform]
            transformed_image = transform_fn(image=image)['image']
            augmented_image_name = f'{image_name}_{transform}.{image_extension}'

            cv2.imwrite(join(images_path, augmented_image_name), cv2.cvtColor(transformed_image, cv2.COLOR_RGB2BGR))