from config import Config
from flask import Flask, g

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)

    from app.errors import blueprint as errors_blueprint
    app.register_blueprint(errors_blueprint)

    @app.before_request
    def load_services():
        if not hasattr(g, 'logo_detection_service'):
            from app.images.services.logo_detection_service import LogoDetectionService
            g.logo_detection_service = LogoDetectionService()

    from app.images import blueprint as images_blueprint
    app.register_blueprint(images_blueprint)

    return app
