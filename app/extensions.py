from flask import request, current_app
from functools import wraps

from app.errors.handlers import unauthorized

def api_key_required():
    def decorator(function: callable):
        @wraps(function)
        def wrapper(*args, **kwargs):
            api_key_header = request.headers.get('X-API-Key')

            if api_key_header != current_app.config['API_KEY']:
                return unauthorized('Invalid API key')

            return function(*args, **kwargs)

        return wrapper

    return decorator