from werkzeug.http import HTTP_STATUS_CODES
from werkzeug.exceptions import HTTPException

from app.errors import blueprint

CODES = {
    400: 'BAD_REQUEST',
    401: 'UNAUTHORIZED',
    422: 'UNPROCESSABLE_ENTITY',
    500: 'INTERNAL_SERVER_ERROR'
}

def error_response(status_code, message, details=None):
    payload = {
        'code': CODES.get(status_code, 'UNKNOWN_ERROR'),
        'message': message
    }

    if details:
        payload['details'] = details

    return payload, status_code

def bad_request(message: str):
    return error_response(400, message)

def unauthorized(message: str):
    return error_response(401, message)

def unprocessable_entity(message: str, details: list[str] | None = None):
    return error_response(422, message, details)

def internal_server_error(message: str):
    return error_response(500, message)

@blueprint.errorhandler(HTTPException)
def handle_exception(exception: HTTPException):
    return error_response(exception.code, exception.description)
