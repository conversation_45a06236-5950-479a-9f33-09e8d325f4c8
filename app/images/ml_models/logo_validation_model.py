from PIL.Image import Image
from threading import Lock
from keras.models import load_model

class LogoValidationModel:
    def __init__(self, model_path: str):
        self._model_path = model_path
        self._model = None
        self._lock = Lock()
        self.input_width = None
        self.input_height = None
    
    def _get_model(self):
        if self._model is None:
            with self._lock:
                if self._model is None:
                    self._model = load_model(self._model_path, compile=False)
                    _, self.input_width, self.input_height, _ = self._model.input.shape
        return self._model
    
    def predict(self, images: list[Image]):
        model = self._get_model()
        
        return model(images)
