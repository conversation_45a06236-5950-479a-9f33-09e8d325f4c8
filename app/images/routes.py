from flask import request

from app.images import blueprint
from app.errors.handlers import bad_request, internal_server_error, unprocessable_entity

@blueprint.route('/validate', methods=['POST'])
def validate_images():
    image_files = request.files.getlist('images')

    if not image_files or len(image_files) == 0:
        return bad_request('No images provided')

    try:
        corners = []

        for image_file in image_files:
            with Image.open(image_file.stream) as image:
                if image.format not in ['JPEG', 'PNG', 'WEBP']:
                    raise InvalidImageFormatException(f'Invalid image format "{image.format}" for "{image_file.filename}"')

                corners.extend([tf.convert_to_tensor(corner, dtype=tf.float32) for corner in crop_corners(image, input_width, input_height)])

        corners = preprocess_input(tf.convert_to_tensor(corners))

        result = model(corners, training=False)
        result = tf.reduce_any(tf.reshape(result, (-1, 4)) > 0.5, axis=1)

        invalid_images = [image_files[index].filename for index, is_valid in enumerate(result) if not is_valid]

        if len(invalid_images) > 0:
            return unprocessable_entity(f'Logo not found in images', exception.images)

        return '', 204
    except InvalidImageFormatException as exception:
        return bad_request(str(exception))
    except Exception as exception:
        return internal_server_error(f'Processing failed')