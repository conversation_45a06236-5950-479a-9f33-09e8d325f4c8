from flask import request, g
from PIL import Image

from app.images import blueprint
from app.images.services.logo_validation_service import LogoValidationService
from app.errors.handlers import bad_request, internal_server_error, unprocessable_entity
from app.extensions import api_key_required
from helpers.image import crop_corners

def validate_request():
    image_files = request.files.getlist('images')

    if not image_files or len(image_files) == 0:
        return (False, None, bad_request('No images provided'))
    
    images: list[Image.Image] = []

    for image_file in image_files:
        try:
            image = Image.open(image_file.stream)
            if image.format not in ['JPEG', 'PNG', 'WEBP']:
                return (False, None, bad_request(f'Invalid image format "{image.format}" for "{image_file.filename}"'))
            
            image.filename = image_file.filename
            images.append(image)
        except Exception:
            return (False, None, bad_request(f'Invalid image "{image_file.filename}"'))

    return (True, images, None)

@blueprint.route('/validate', methods=['POST'])
@api_key_required()
def validate_images():
    (is_valid, images, error) = validate_request()

    if not is_valid:
        return error
    
    try:
        invalid_images = g.logo_detection_service.validate_images(images)

        if len(invalid_images) > 0:
            return unprocessable_entity(f'Logo not found in images', invalid_images)

        return '', 204
    except Exception:
        return internal_server_error('Processing error')
    finally:
        for image in images:
            image.close()
