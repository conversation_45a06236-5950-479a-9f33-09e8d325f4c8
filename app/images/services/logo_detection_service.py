import tensorflow as tf

from PIL.Image import Image
from keras.applications.resnet_v2 import preprocess_input

from helpers.image import crop_corners

class LogoDetectionService:
    def __init__(self, model_path: str):
        self.model = tf.keras.models.load_model(model_path, compile=False)

    def validate_images(self, image_files: list[Image]):
        corners = [crop_corners(image_file, 224, 224) for image_file in image_files]
        corners = preprocess_input(tf.convert_to_tensor(corners))

        result = model(corners, training=False)
        result = tf.reduce_any(tf.reshape(result, (-1, 4)) > 0.5, axis=1)

        return [image_files[index].filename for index, is_valid in enumerate(result) if not is_valid]