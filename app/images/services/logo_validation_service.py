import tensorflow as tf

from PIL.Image import Image
from keras.applications.resnet_v2 import preprocess_input

from app.images.ml_models.logo_validation_model import LogoValidationModel
from helpers.image import crop_corners

class LogoValidationService:
    def __init__(self, model: LogoValidationModel):
        self.model = model

    def validate_images(self, images: list[Image]):
        corners = []

        for image in images:
            for corner in crop_corners(image, 224, 224):
                corners.append(tf.convert_to_tensor(corner, dtype=tf.float32))

        corners = preprocess_input(tf.convert_to_tensor(corners))

        result = self.model.predict(corners)
        result = tf.reduce_any(tf.reshape(result, (-1, 4)) > 0.5, axis=1)

        return [images[index].filename for index, is_valid in enumerate(result) if not is_valid]